# ===============================================================================
# KITCO RESEARCH AI - ENVIRONMENT VARIABLES TEMPLATE
# ===============================================================================
# Copy this file to .env and fill in your actual values
# NEVER commit .env files to version control!
#
# Usage:
#   cp .env.example .env
#   # Edit .env with your actual values
#
# Security Notes:
# - Use strong, unique values for all secrets
# - Rotate keys regularly in production
# - Use different values for development/staging/production
# ===============================================================================

# ===============================================================================
# CORE APPLICATION SECRETS
# ===============================================================================

# Flask Secret Key (REQUIRED)
# Used for session encryption, CSRF protection, and security tokens
# Generate with: python -c "import secrets; print(secrets.token_urlsafe(32))"
SECRET_KEY=your-super-secret-key-change-this-immediately

# Application Environment
# Options: development, staging, production
ENVIRONMENT=development

# Debug Mode (development only)
# NEVER set to true in production!
DEBUG=true

# ===============================================================================
# LLM API KEYS
# ===============================================================================

# OpenAI API Key
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-openai-api-key-here

# OpenAI-Compatible Endpoint API Key (e.g., OpenRouter)
# Get from your provider (e.g., https://openrouter.ai/keys)
OPENAI_ENDPOINT_API_KEY=sk-or-your-endpoint-api-key-here

# ===============================================================================
# SEARCH ENGINE API KEYS
# ===============================================================================

# SerpAPI Key (Google Search)
# Get from: https://serpapi.com/dashboard
SERPAPI_API_KEY=your-serpapi-key-here

# Brave Search API Key
# Get from: https://api.search.brave.com/app/keys
BRAVE_API_KEY=BSA-your-brave-api-key-here

# Google Programmable Search Engine
# Get from: https://developers.google.com/custom-search/v1/introduction
GOOGLE_PSE_API_KEY=your-google-pse-api-key-here
GOOGLE_PSE_ENGINE_ID=your-search-engine-id-here

# ===============================================================================
# DATABASE & STORAGE
# ===============================================================================

# Database URL (optional - defaults to local SQLite)
# Examples:
#   SQLite: sqlite:///data/ldr.db
#   PostgreSQL: postgresql://user:pass@localhost/dbname
#   MySQL: mysql://user:pass@localhost/dbname
# DATABASE_URL=sqlite:///data/ldr.db

# Database Encryption Key (for sensitive data)
# Generate with: python -c "import secrets; print(secrets.token_urlsafe(32))"
# DATABASE_ENCRYPTION_KEY=your-database-encryption-key-here

# ===============================================================================
# WEB SERVER CONFIGURATION
# ===============================================================================

# Server Host (default: 0.0.0.0 for all interfaces)
# Use 127.0.0.1 for localhost-only access
HOST=0.0.0.0

# Server Port (default: 8765)
PORT=8765

# HTTPS Configuration (production only)
# SSL_CERT_PATH=/path/to/certificate.crt
# SSL_KEY_PATH=/path/to/private.key

# ===============================================================================
# SECURITY SETTINGS
# ===============================================================================

# Secrets Encryption Key (production only)
# Generate with: python scripts/generate_secrets.py
# KITCO_SECRETS_KEY=your-base64-encoded-encryption-key

# Session Configuration
SESSION_TIMEOUT_MINUTES=60
MAX_LOGIN_ATTEMPTS=5

# Rate Limiting
API_RATE_LIMIT=100

# CORS Origins (comma-separated)
ALLOWED_ORIGINS=localhost,127.0.0.1

# ===============================================================================
# LOGGING & MONITORING
# ===============================================================================

# Log Level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log File Path (optional)
# LOG_FILE=logs/kitco_research_ai.log

# Enable Performance Monitoring
# ENABLE_MONITORING=false

# ===============================================================================
# EXTERNAL SERVICES
# ===============================================================================

# Email Configuration (for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_USE_TLS=true

# Redis Configuration (for caching)
# REDIS_URL=redis://localhost:6379/0

# ===============================================================================
# DEVELOPMENT SETTINGS
# ===============================================================================

# Development-specific settings (ignored in production)

# Enable Development Tools
ENABLE_DEV_TOOLS=true

# Mock External APIs (for testing)
MOCK_APIS=false

# Development Database (separate from production)
DEV_DATABASE_URL=sqlite:///data/dev_ldr.db

# ===============================================================================
# FEATURE FLAGS
# ===============================================================================

# Enable/disable specific features
ENABLE_FACT_CHECKING=true
ENABLE_FULL_SEARCH=true
ENABLE_NOTIFICATIONS=true
ENABLE_EXPORT_PDF=true
ENABLE_EXPORT_WORD=true

# ===============================================================================
# PERFORMANCE TUNING
# ===============================================================================

# Search Configuration
MAX_SEARCH_RESULTS=50
SEARCH_TIMEOUT_SECONDS=30
MAX_CONCURRENT_SEARCHES=3

# LLM Configuration
LLM_TIMEOUT_SECONDS=120
MAX_TOKENS=30000
TEMPERATURE=0.7

# Cache Configuration
ENABLE_CACHING=true
CACHE_TTL_SECONDS=3600

# ===============================================================================
# BACKUP & RECOVERY
# ===============================================================================

# Backup Configuration
# BACKUP_ENABLED=true
# BACKUP_INTERVAL_HOURS=24
# BACKUP_RETENTION_DAYS=30
# BACKUP_STORAGE_PATH=/path/to/backups

# ===============================================================================
# SECURITY NOTES
# ===============================================================================
#
# 1. NEVER commit .env files to version control
# 2. Use different values for development/staging/production
# 3. Rotate secrets regularly (at least every 90 days)
# 4. Use strong, unique passwords and keys
# 5. Enable 2FA on all service accounts
# 6. Monitor for leaked credentials
# 7. Use environment-specific configurations
# 8. Encrypt sensitive data at rest
# 9. Use HTTPS in production
# 10. Regularly audit access logs
#
# ===============================================================================
# QUICK SETUP COMMANDS
# ===============================================================================
#
# 1. Copy this file:
#    cp .env.example .env
#
# 2. Generate secure keys:
#    python scripts/generate_secrets.py
#
# 3. Edit .env with your values:
#    nano .env  # or your preferred editor
#
# 4. Validate configuration:
#    python scripts/security_check.py
#
# 5. Start the application:
#    python app.py
#
# ===============================================================================
