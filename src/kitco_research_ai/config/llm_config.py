import os

from langchain_community.llms import VLLM
from langchain_core.language_models import FakeListChatModel
from langchain_ollama import ChatOllama
from langchain_openai import ChatOpenAI
from loguru import logger

from ..utilities.db_utils import get_db_setting
from ..utilities.search_utilities import remove_think_tags
from ..utilities.url_utils import normalize_url

# Import secure secrets management
try:
    import sys
    from pathlib import Path
    # Add config directory to path
    config_dir = Path(__file__).parent.parent.parent.parent / "config"
    if str(config_dir) not in sys.path:
        sys.path.insert(0, str(config_dir))

    # Import environment loader to ensure .env files are loaded
    from environment import env_loader
    from secrets_manager import get_secret
    SECRETS_AVAILABLE = True
    logger.info("Secure secrets management loaded successfully")
except ImportError as e:
    logger.warning(f"Secure secrets management not available: {e}, falling back to basic environment variables")
    SECRETS_AVAILABLE = False

# Valid provider options
VALID_PROVIDERS = [
    "ollama",
    "openai",
    "vllm",
    "openai_endpoint",
    "lmstudio",
    "none",
]


def validate_openai_api_key(api_key: str) -> bool:
    """
    Validate OpenAI API key format.

    Args:
        api_key: The API key to validate

    Returns:
        True if the API key format is valid, False otherwise
    """
    if not api_key or not isinstance(api_key, str):
        return False

    # New project-based format: sk-proj-... (164 characters total)
    if api_key.startswith("sk-proj-") and len(api_key) == 164:
        return True

    # Legacy format: sk-... (51 characters total)
    if api_key.startswith("sk-") and len(api_key) == 51:
        return True

    return False


def get_secure_api_key(provider: str, config_key: str = None) -> str:
    """
    Get API key securely from environment variables or encrypted storage.

    Args:
        provider: Provider name (openai, etc.)
        config_key: Optional database config key to check first

    Returns:
        API key string or None if not found
    """
    # First try the secure secrets manager if available
    if SECRETS_AVAILABLE:
        # Try direct secret lookup
        env_keys = {
            'openai': 'OPENAI_API_KEY',
            'openai_endpoint': 'OPENAI_ENDPOINT_API_KEY',
            'brave': 'BRAVE_API_KEY',
            'serpapi': 'SERPAPI_API_KEY',
            'google_pse': 'GOOGLE_PSE_API_KEY',
        }

        env_key = env_keys.get(provider)
        if env_key:
            api_key = get_secret(env_key)
            if api_key:
                return api_key

    # Try database setting if config_key provided
    if config_key:
        db_value = get_db_setting(config_key)
        if db_value and db_value != config_key.upper():
            # If it's not just the placeholder, try to resolve it
            if SECRETS_AVAILABLE:
                resolved = get_secret(db_value)
                if resolved and resolved != db_value:
                    return resolved
            # Try as environment variable
            env_value = os.getenv(db_value)
            if env_value:
                return env_value
            # If it doesn't look like an env var reference, use as-is
            if not db_value.isupper() or '_' not in db_value:
                return db_value

    # Fallback to direct environment variable lookup
    env_keys = {
        'openai': ['OPENAI_API_KEY'],
        'openai_endpoint': ['OPENAI_ENDPOINT_API_KEY'],
        'brave': ['BRAVE_API_KEY'],
        'serpapi': ['SERPAPI_API_KEY'],
        'google_pse': ['GOOGLE_PSE_API_KEY'],
    }

    for env_key in env_keys.get(provider, []):
        value = os.getenv(env_key)
        if value:
            return value

    return None


def get_llm(model_name=None, temperature=None, provider=None, openai_endpoint_url=None):
    """
    Get LLM instance based on model name and provider.

    Args:
        model_name: Name of the model to use (if None, uses database setting)
        temperature: Model temperature (if None, uses database setting)
        provider: Provider to use (if None, uses database setting)
        openai_endpoint_url: Custom endpoint URL to use (if None, uses database
            setting)

    Returns:
        A LangChain LLM instance with automatic think-tag removal
    """

    # Use database values for parameters if not provided
    if model_name is None:
        model_name = get_db_setting("llm.model", "gemma:latest")
    if temperature is None:
        temperature = get_db_setting("llm.temperature", 0.7)
    if provider is None:
        provider = get_db_setting("llm.provider", "ollama")

    # Clean model name: remove quotes and extra whitespace
    if model_name:
        model_name = model_name.strip().strip("\"'").strip()

    # Clean provider: remove quotes and extra whitespace
    if provider:
        provider = provider.strip().strip("\"'").strip()

    # Normalize provider: convert to lowercase
    provider = provider.lower() if provider else None

    # Validate provider
    if provider not in VALID_PROVIDERS:
        logger.error(f"Invalid provider in settings: {provider}")
        raise ValueError(
            f"Invalid provider: {provider}. Must be one of: {VALID_PROVIDERS}"
        )
    logger.info(
        f"Getting LLM with model: {model_name}, temperature: {temperature}, provider: {provider}"
    )

    # Common parameters for all models
    common_params = {
        "temperature": temperature,
    }

    # Get context window size from settings
    context_window_size = get_db_setting("llm.context_window_size", 32000)

    if get_db_setting("llm.supports_max_tokens", True):
        # Use 80% of context window to leave room for prompts
        max_tokens = min(
            get_db_setting("llm.max_tokens", 30000), int(context_window_size * 0.8)
        )
        common_params["max_tokens"] = max_tokens

    # Handle different providers
    if provider == "openai":
        api_key = get_secure_api_key("openai", "llm.openai.api_key")
        if not api_key:
            logger.warning("OPENAI_API_KEY not found. Falling back to default model.")
            return get_fallback_model(temperature)

        # Validate API key format
        if not validate_openai_api_key(api_key):
            logger.warning(f"Invalid OpenAI API key format. Expected 'sk-proj-...' (164 chars) or 'sk-...' (51 chars). Falling back to default model.")
            return get_fallback_model(temperature)

        try:
            llm = ChatOpenAI(model=model_name, api_key=api_key, **common_params)
            return wrap_llm_without_think_tags(llm, model_name, provider)
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI model: {str(e)}")
            return get_fallback_model(temperature)

    elif provider == "openai_endpoint":
        api_key = get_secure_api_key("openai_endpoint", "llm.openai_endpoint.api_key")
        if not api_key:
            logger.warning(
                "OPENAI_ENDPOINT_API_KEY not found. Falling back to default model."
            )
            return get_fallback_model(temperature)

        # Get endpoint URL from settings
        if openai_endpoint_url is None:
            openai_endpoint_url = get_db_setting(
                "llm.openai_endpoint.url", "https://openrouter.ai/api/v1"
            )

        try:
            llm = ChatOpenAI(
                model=model_name,
                api_key=api_key,
                openai_api_base=openai_endpoint_url,
                **common_params,
            )
            return wrap_llm_without_think_tags(llm, model_name, provider)
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI endpoint model: {str(e)}")
            return get_fallback_model(temperature)

    elif provider == "vllm":
        try:
            llm = VLLM(
                model=model_name,
                trust_remote_code=True,
                max_new_tokens=128,
                top_k=10,
                top_p=0.95,
                temperature=temperature,
            )
            return wrap_llm_without_think_tags(llm, model_name, provider)
        except Exception:
            logger.exception("Error loading VLLM model")
            return get_fallback_model(temperature)

    elif provider == "ollama":
        try:
            # Use the configurable Ollama base URL
            raw_base_url = get_db_setting("llm.ollama.url", "http://localhost:11434")
            base_url = (
                normalize_url(raw_base_url)
                if raw_base_url
                else "http://localhost:11434"
            )

            # Check if Ollama is available before trying to use it
            if not is_ollama_available():
                logger.error(
                    f"Ollama not available at {base_url}. Falling back to dummy model."
                )
                return get_fallback_model(temperature)

            # Check if the requested model exists
            import requests

            try:
                logger.info(f"Checking if model '{model_name}' exists in Ollama")
                response = requests.get(f"{base_url}/api/tags", timeout=3.0)
                if response.status_code == 200:
                    # Handle both newer and older Ollama API formats
                    data = response.json()
                    models = []
                    if "models" in data:
                        # Newer Ollama API
                        models = data.get("models", [])
                    else:
                        # Older Ollama API format
                        models = data

                    # Get list of model names
                    model_names = [m.get("name", "").lower() for m in models]
                    logger.info(
                        f"Available Ollama models: {', '.join(model_names[:5])}{' and more' if len(model_names) > 5 else ''}"
                    )

                    if model_name.lower() not in model_names:
                        logger.error(
                            f"Model '{model_name}' not found in Ollama. Available models: {', '.join(model_names[:5])}"
                        )
                        return get_fallback_model(temperature)
            except Exception:
                logger.exception(f"Error checking for model '{model_name}' in Ollama")
                # Continue anyway, let ChatOllama handle potential errors

            logger.info(
                f"Creating ChatOllama with model={model_name}, base_url={base_url}"
            )
            try:
                llm = ChatOllama(model=model_name, base_url=base_url, **common_params)
                # Test invoke to validate model works
                logger.info("Testing Ollama model with simple invocation")
                test_result = llm.invoke("Hello")
                logger.info(
                    f"Ollama test successful. Response type: {type(test_result)}"
                )
                return wrap_llm_without_think_tags(llm, model_name, provider)
            except Exception:
                logger.exception("Error creating or testing ChatOllama")
                return get_fallback_model(temperature)
        except Exception:
            logger.exception("Error in Ollama provider section")
            return get_fallback_model(temperature)

    elif provider == "lmstudio":
        # LM Studio supports OpenAI API format, so we can use ChatOpenAI directly
        # Check for Docker environment variable first, then database setting
        lmstudio_url = os.getenv("LM_STUDIO_URL") or get_db_setting("llm.lmstudio.url", "http://localhost:1234")

        # Always check available models to validate the requested model
        available_models = []
        try:
            import requests
            logger.info(f"Checking available models in LM Studio at {lmstudio_url}/v1/models")
            response = requests.get(f"{lmstudio_url}/v1/models", timeout=5.0)
            if response.status_code == 200:
                models_data = response.json()
                logger.info(f"LM Studio models response: {models_data}")

                if "data" in models_data and len(models_data["data"]) > 0:
                    available_models = [model.get("id", "") for model in models_data["data"]]
                    logger.info(f"Available LM Studio models: {available_models}")

                    # Check if requested model is available
                    if model_name and model_name in available_models:
                        logger.info(f"Requested model '{model_name}' is available in LM Studio")
                    elif available_models:
                        # If Just-in-Time loading is enabled, try the requested model anyway
                        logger.info(f"Model '{model_name}' not in loaded models list, but trying anyway (Just-in-Time loading may be enabled)")
                    else:
                        logger.warning("No models found in LM Studio response")
                        return get_fallback_model(temperature)
                else:
                    logger.warning("No models found in LM Studio response")
                    # If Just-in-Time loading is enabled, we can still try the requested model
                    logger.info(f"Attempting to use model '{model_name}' with Just-in-Time loading")
            else:
                logger.warning(f"LM Studio API returned status code: {response.status_code}")
                return get_fallback_model(temperature)
        except Exception as e:
            logger.error(f"Failed to check LM Studio models: {str(e)}")
            return get_fallback_model(temperature)

        # Auto-detect available model if using generic model names
        if model_name in ["model1", "model2", "auto", "default"] or not model_name:
            try:
                if available_models:
                    # Use the first available model
                    detected_model = available_models[0]
                    logger.info(f"Auto-detected LM Studio model: {detected_model}")
                    model_name = detected_model
                else:
                    logger.warning("No models available for auto-detection")
                    return get_fallback_model(temperature)
            except Exception as e:
                logger.error(f"Failed to auto-detect LM Studio model: {str(e)}")
                return get_fallback_model(temperature)

        logger.info(f"Creating LM Studio LLM with model: {model_name}")
        try:
            llm = ChatOpenAI(
                model=model_name,
                api_key="lm-studio",  # LM Studio doesn't require a real API key
                base_url=f"{lmstudio_url}/v1",  # Use the configured URL with /v1 endpoint
                temperature=temperature,
                max_tokens=max_tokens,  # Use calculated max_tokens based on context size
            )
            return wrap_llm_without_think_tags(llm, model_name, provider)
        except Exception as e:
            logger.error(f"Failed to create LM Studio LLM: {str(e)}")
            return get_fallback_model(temperature)

    else:
        return wrap_llm_without_think_tags(get_fallback_model(temperature), "fallback", "none")


def get_fallback_model(temperature=None):
    """Create a dummy model for when no providers are available"""
    return FakeListChatModel(
        responses=[
            "No language models are available. Please install Ollama or set up API keys."
        ]
    )


def wrap_llm_without_think_tags(llm, model_name=None, provider=None):
    """Create a wrapper class that processes LLM outputs with remove_think_tags"""

    class ProcessingLLMWrapper:
        def __init__(self, base_llm, model_name=None, provider=None):
            self.base_llm = base_llm
            # Store model information for access by search system
            self._model_name = model_name
            self._provider = provider

        def invoke(self, *args, **kwargs):
            response = self.base_llm.invoke(*args, **kwargs)

            # Process the response content if it has a content attribute
            if hasattr(response, "content"):
                response.content = remove_think_tags(response.content)
            elif isinstance(response, str):
                response = remove_think_tags(response)

            return response

        # Pass through any other attributes to the base LLM
        def __getattr__(self, name):
            return getattr(self.base_llm, name)

    return ProcessingLLMWrapper(llm, model_name, provider)


def get_available_provider_types():
    """Return available model providers (only visible ones for production use)"""
    providers = {}

    # Always include OpenAI as the default provider
    if is_openai_available():
        providers["openai"] = "OpenAI API (Default)"

    # Include local providers if available
    if is_ollama_available():
        providers["ollama"] = "Ollama (Local)"

    if is_lmstudio_available():
        providers["lmstudio"] = "LM Studio (Local)"

    # Hidden providers for future development (not shown in UI)
    # These are still functional but not exposed to users
    # - vLLM: for future local deployment
    # - Custom OpenAI Endpoint: for future API compatibility

    # Default fallback
    if not providers:
        providers["openai"] = "OpenAI API (Default - requires API key)"

    return providers


def is_openai_available():
    """Check if OpenAI is available - always return True as it's the default provider"""
    # OpenAI is always available as the default provider, even without API key
    # Users can configure the API key later
    return True


def is_openai_endpoint_available():
    """Check if OpenAI endpoint is available"""
    try:
        api_key = get_db_setting("llm.openai_endpoint.api_key")
        return bool(api_key)
    except Exception:
        return False


def is_ollama_available():
    """Check if Ollama is running"""
    try:
        import requests

        raw_base_url = get_db_setting("llm.ollama.url", "http://localhost:11434")
        base_url = (
            normalize_url(raw_base_url) if raw_base_url else "http://localhost:11434"
        )
        logger.info(f"Checking Ollama availability at {base_url}/api/tags")

        try:
            response = requests.get(f"{base_url}/api/tags", timeout=3.0)
            if response.status_code == 200:
                logger.info(f"Ollama is available. Status code: {response.status_code}")
                # Log first 100 chars of response to debug
                logger.info(f"Response preview: {str(response.text)[:100]}")
                return True
            else:
                logger.warning(
                    f"Ollama API returned status code: {response.status_code}"
                )
                return False
        except requests.exceptions.RequestException as req_error:
            logger.error(f"Request error when checking Ollama: {str(req_error)}")
            return False
        except Exception:
            logger.exception("Unexpected error when checking Ollama")
            return False
    except Exception:
        logger.exception("Error in is_ollama_available")
        return False


def is_vllm_available():
    """Check if VLLM capability is available"""
    try:
        import torch  # noqa: F401
        import transformers  # noqa: F401

        return True
    except ImportError:
        return False


def is_lmstudio_available():
    """Check if LM Studio is available"""
    try:
        import requests

        # Check for Docker environment variable first, then database setting
        lmstudio_url = os.getenv("LM_STUDIO_URL") or get_db_setting("llm.lmstudio.url", "http://localhost:1234")
        # LM Studio typically uses OpenAI-compatible endpoints
        logger.info(f"Checking LM Studio availability at {lmstudio_url}/v1/models")
        response = requests.get(f"{lmstudio_url}/v1/models", timeout=3.0)
        if response.status_code == 200:
            logger.info("LM Studio is available")
            return True
        else:
            logger.warning(f"LM Studio returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as req_error:
        logger.error(f"Request error when checking LM Studio: {str(req_error)}")
        return False
    except Exception as e:
        logger.exception(f"Unexpected error when checking LM Studio: {str(e)}")
        return False


def get_available_providers():
    """Get dictionary of available providers"""
    return get_available_provider_types()


AVAILABLE_PROVIDERS = get_available_providers()
selected_provider = get_db_setting("llm.provider", "ollama").lower()

# Log which providers are available
logger.info(f"Available providers: {list(AVAILABLE_PROVIDERS.keys())}")

# Check if selected provider is available
if selected_provider not in AVAILABLE_PROVIDERS and selected_provider != "none":
    logger.warning(f"Selected provider {selected_provider} is not available.")
